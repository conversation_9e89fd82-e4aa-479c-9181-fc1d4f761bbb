<!--
 * @Description: 豫园股份登录页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-05-15 09:25:55
 * @LastEditTime: 2025-08-01 14:34:12
-->

<template>
  <div
    class="login-container"
    :style="
      tenantBgImg
        ? `background-image: url(${tenantBgImg}) !important`
        : `background-image: url(https://training-voc.obs.cn-north-4.myhuaweicloud.com:443/yygf-login.jpg) !important`
    "
  >
    <div class="login_main">
      <el-form
        ref="loginRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            type="text"
            size="large"
            auto-complete="off"
            placeholder="账号"
          >
            <template #prefix
              ><svg-icon icon-class="user" class="el-input__icon input-icon"
            /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            size="large"
            auto-complete="off"
            placeholder="密码"
            @keyup.enter="handleLogin"
          >
            <template #prefix
              ><svg-icon
                icon-class="password"
                class="el-input__icon input-icon"
            /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="code" v-if="captchaEnabled">
          <el-input
            v-model="loginForm.code"
            size="large"
            auto-complete="off"
            placeholder="验证码"
            style="width: 63%"
            @keyup.enter="handleLogin"
          >
            <template #prefix
              ><svg-icon
                icon-class="validCode"
                class="el-input__icon input-icon"
            /></template>
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" @click="getCode" class="login-code-img" />
          </div>
        </el-form-item>
        <el-form-item style="width: 100%; margin-top: 30px">
          <img
            src="@/assets/images/yygf-login-btn.png"
            class="login-btn"
            @click.prevent="handleLogin"
          />
        </el-form-item>
      </el-form>
    </div>
    <!-- <div class="tips_main">
      <div class="tips_title">培训信息</div>
      <p class="tips_content">1、豫园股份工伤预防培训</p>
      <p class="tips_content">2、2024.8.1 - 8.31</p>
      <p class="tips_content">3、在线学习 + 线下授课</p>
      <p class="tips_content" style="line-height: 27px">
        4、平台须知：学员可通过电脑端和手机端进行在线学习，登录后点击顶部“学习”菜单，完成“我的任务”中所有内容和考试即可。 
      </p>
    </div> -->
    <!--  底部  -->
    <div class="el-login-footer" v-if="!domainName">
      上海柏科管理咨询股份有限公司 版权所有 严禁复制 网站备案号：
      <a href="" rel="noreferrer" target="_blank">沪公网安备31010902100983号</a>
    </div>
  </div>
</template>

<script setup>
  import { getCodeImg } from "@/api/login"
  import Cookies from "js-cookie"
  import useUserStore from "@/store/modules/user"
  import { getToken } from "@/utils/auth"
  import useTenantStore from "@/store/modules/tenant"
  import useSettingsStore from "@/store/modules/settings"
  import { handleThemeStyle } from "@/utils/theme"

  const settingsStore = useSettingsStore()
  const tenantStore = useTenantStore()
  const userStore = useUserStore()
  const router = useRouter()
  const { proxy } = getCurrentInstance()
  const env = import.meta.env.VITE_APP_ENV

  const { tenantLogo, domainName, tenantBgImg, tenantName } =
    storeToRefs(tenantStore)
  const loginForm = ref({
    testTenant: "master",
    username: "",
    password: "",
    mobile: "",
    code: "",
    uuid: ""
  })

  const loginRules = {
    testTenant: [
      { required: true, trigger: "blur", message: "请输入租户名称" }
    ],
    username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
    password: [{ required: true, trigger: "blur", message: "请输入您的密码" }]
  }

  const codeUrl = ref("")
  const loading = ref(false)
  // 验证码开关
  const captchaEnabled = ref(true)
  // 注册开关
  const register = ref(false)
  const redirectUrl = ref(null)

  function handleLogin() {
    proxy.$refs.loginRef.validate(valid => {
      if (valid) {
        loading.value = true
        // 将租户信息存入请求头
        if (loginForm.value.testTenant) {
          localStorage.setItem("testTenant", loginForm.value.testTenant)
        }
        // 调用action的登录方法
        userStore
          .login(loginForm.value)
          .then(publicKey => {
            if (redirectUrl.value.indexOf("http") !== -1) {
              window.location.href = `${
                redirectUrl.value
              }?token=${getToken()}&testTenant=${loginForm.value.testTenant}`
            } else {
              router.push({ path: "/" })
            }
          })
          .catch(() => {
            loading.value = false
            // 重新获取验证码
            if (captchaEnabled.value) {
              getCode()
            }
          })
      }
    })
  }

  function getCode() {
    getCodeImg().then(res => {
      captchaEnabled.value =
        res.captchaEnabled === undefined ? true : res.captchaEnabled
      if (captchaEnabled.value) {
        codeUrl.value = "data:image/gif;base64," + res.img
        loginForm.value.uuid = res.uuid
      }
    })
  }

  // redirect是获取哪个系统域名 比如:http://127.0.0.1:8080 方便登录成功以后跳转相应的系统
  if (window.location.href.indexOf("redirect") >= 0) {
    //如果url中包含redirect   split？分割成数组，取第二个
    let redirect = window.location.href.split("?")[1]
    redirect = redirect.substring(9) // 截取字符串第9位开始截取到最后
    redirectUrl.value = redirect
  }

  const changeYYGFStyle = () => {
    const loginCtn = document.querySelector(".login-container")
    // 给loginCtn动态添加样式
    loginCtn.style.backgroundSize = "100% 100%"
    const yygfColor = "#f7943d"
    nextTick(() => {
      document
        .getElementsByTagName("body")[0]
        .style.setProperty("--primaryColor", "#f7943d")
      settingsStore.changeSetting({ key: "theme", value: yygfColor })
      handleThemeStyle(yygfColor)
    })
  }

  onMounted(() => {
    getCode()
    if (domainName.value === "yygf") {
      changeYYGFStyle()
    }
  })
</script>

<style lang="scss" scoped>
  $primaryColor: var(--primaryColor, #589cfd);
  .login-container {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: top;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;

    .login_main {
      position: absolute;
      top: 250px;
      right: 27%;
      padding: 30px 0;
    }

    .tips_main {
      position: absolute;
      top: 300px;
      min-height: 300px;
      right: 15%;
      padding: 15px;
      width: 350px;
      background-color: rgba($color: #f7d395, $alpha: 0.8);
      color: #da3018;
      font-weight: bold;
      display: flex;
      justify-content: center;
      flex-direction: column;
      .tips_title {
        margin: 0 auto;
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 15px;
      }

      .tips_content {
        font-size: 16px;
        line-height: 20px;
        margin-bottom: 15px;
      }
    }
  }

  .login-form {
    background-image: url("@/assets/images/yygf-login-bg.png") !important;
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-position: top;
    border-radius: 6px;
    width: 450px;
    padding: 180px 45px 5px 35px;
    .el-input {
      height: 40px;
      input {
        height: 40px;
      }
    }
    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 0px;
    }

    .login-btn {
      margin: 0 auto;
      width: 80%;
      cursor: pointer;
    }
  }
  .login-code {
    width: 33%;
    height: 40px;
    float: right;
    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }
  .el-login-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: black;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }
  .login-code-img {
    height: 40px;
    padding-left: 12px;
  }

  :deep(.el-input--large .el-input__wrapper) {
    margin-left: 10px;
    padding: 1px 1px;
  }

  :deep(.el-input__prefix) {
    padding-left: 10px;
  }

  :deep(.el-input--large .el-input__inner) {
    padding-left: 10px;
  }
</style>
