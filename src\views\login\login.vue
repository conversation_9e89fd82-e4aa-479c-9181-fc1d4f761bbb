<!--
 * @Description: 登录页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-11-07 16:43:02
 * @LastEditTime: 2025-01-15 09:59:49
-->
<template>
  <div
    class="login-container"
    :style="
      tenantBgImg
        ? `background-image: url(${tenantBgImg}) !important`
        : `background-image: url(https://training-voc.obs.cn-north-4.myhuaweicloud.com:443/background.png) !important`
    "
  >
    <div class="login-main">
      <div class="login-main-title">
        <img
          class="logo-img"
          :src="
            tenantLogo ||
            'https://training-voc.obs.cn-north-4.myhuaweicloud.com:443/beckwell.png'
          "
          alt=""
        />
        <div class="login-main-title-name">{{
          tenantName ? tenantName : "在线学习平台"
        }}</div>
      </div>
      <div class="login-main-body">
        <div class="login-main-body-left">
          <img
            src="https://training-voc.obs.cn-north-4.myhuaweicloud.com:443/login_left.png"
          />
        </div>

        <div class="login-main-body-right">
          <div class="login-main-body-right-title">登录</div>
          <div class="login-main-body-right-form">
            <el-form
              ref="loginRef"
              :model="loginForm"
              :rules="loginRules"
              class="login-form"
            >
              <el-form-item
                v-if="!productionEnvList.includes(env)"
                prop="testTenant"
              >
                <el-input
                  v-model="loginForm.testTenant"
                  type="text"
                  size="large"
                  auto-complete="off"
                  placeholder="租户"
                >
                  <template #prefix>
                    <svg-icon
                      icon-class="server"
                      class="el-input__icon input-icon"
                    />
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item prop="username">
                <el-input
                  v-model="loginForm.username"
                  type="text"
                  size="large"
                  auto-complete="off"
                  placeholder="账号"
                >
                  <template #prefix
                    ><svg-icon
                      icon-class="user"
                      class="el-input__icon input-icon"
                  /></template>
                </el-input>
              </el-form-item>
              <el-form-item prop="password">
                <el-input
                  v-model="loginForm.password"
                  type="password"
                  size="large"
                  auto-complete="off"
                  placeholder="密码"
                  @keyup.enter="handleLogin"
                  show-password
                >
                  <template #prefix
                    ><svg-icon
                      icon-class="password"
                      class="el-input__icon input-icon"
                  /></template>
                </el-input>
              </el-form-item>
              <el-form-item style="width: 100%">
                <el-button
                  :loading="loading"
                  size="large"
                  type="primary"
                  style="
                    height: 50px;
                    width: 97%;
                    margin-left: 10px;
                    margin-top: 50px;
                    background: linear-gradient(to right, #78adfd, #3555fc);
                    border-radius: 10px;
                    box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.3);
                  "
                  @click.prevent="handleLogin"
                >
                  <span v-if="!loading">马上登录</span>
                  <span v-else>登 录 中...</span>
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>
    <!--  底部  -->
    <div class="el-login-footer" v-if="!domainName">
      上海柏科管理咨询股份有限公司 版权所有 严禁复制 网站备案号：<a
        href="https://beian.miit.gov.cn/#/Integrated/index"
        target="_blank"
      >
        沪ICP备2021018439号-2
      </a>
      <a href="" rel="noreferrer" target="_blank">沪公网安备31010902100983号</a>
    </div>
  </div>
</template>

<script setup>
  import { getCodeImg } from "@/api/login"
  import Cookies from "js-cookie"
  import useUserStore from "@/store/modules/user"
  import { getToken } from "@/utils/auth"
  import useTenantStore from "@/store/modules/tenant"
  import useSettingsStore from "@/store/modules/settings"
  import { handleThemeStyle } from "@/utils/theme"
  import { productionEnvList } from "@/utils/constant"

  const settingsStore = useSettingsStore()
  const tenantStore = useTenantStore()
  const userStore = useUserStore()
  const router = useRouter()
  const { proxy } = getCurrentInstance()
  const env = import.meta.env.VITE_APP_ENV

  const { tenantLogo, domainName, tenantBgImg, tenantName } =
    storeToRefs(tenantStore)
  const loginForm = ref({
    testTenant: domainName.value || "master",
    username: "",
    password: "",
    mobile: "",
    code: "",
    uuid: ""
  })

  const loginRules = {
    testTenant: [
      { required: true, trigger: "blur", message: "请输入租户名称" }
    ],
    username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
    password: [{ required: true, trigger: "blur", message: "请输入您的密码" }]
  }

  const codeUrl = ref("")
  const loading = ref(false)
  // 验证码开关
  const captchaEnabled = ref(true)
  // 注册开关
  const register = ref(false)
  const redirectUrl = ref(null)

  function handleLogin() {
    proxy.$refs.loginRef.validate(valid => {
      if (valid) {
        loading.value = true
        // 将租户信息存入请求头
        if (loginForm.value.testTenant) {
          localStorage.setItem("testTenant", loginForm.value.testTenant)
        }
        // 调用action的登录方法
        userStore
          .login(loginForm.value)
          .then(publicKey => {
            if (redirectUrl.value.indexOf("http") !== -1) {
              window.location.href = `${
                redirectUrl.value
              }?token=${getToken()}&testTenant=${loginForm.value.testTenant}`
            } else {
              router.push({ path: "/" })
            }
          })
          .catch(() => {
            loading.value = false
            // 重新获取验证码
            if (captchaEnabled.value) {
              getCode()
            }
          })
      }
    })
  }

  function getCode() {
    getCodeImg().then(res => {
      captchaEnabled.value =
        res.captchaEnabled === undefined ? true : res.captchaEnabled
      if (captchaEnabled.value) {
        codeUrl.value = "data:image/gif;base64," + res.img
        loginForm.value.uuid = res.uuid
      }
    })
  }

  // redirect是获取哪个系统域名 比如:http://127.0.0.1:8080 方便登录成功以后跳转相应的系统
  if (window.location.href.indexOf("redirect") >= 0) {
    //如果url中包含redirect   split？分割成数组，取第二个
    let redirect = window.location.href.split("?")[1]
    redirect = redirect.substring(9) // 截取字符串第9位开始截取到最后
    redirectUrl.value = redirect
  }

  const changeYYGFStyle = () => {
    const loginCtn = document.querySelector(".login-container")
    // 给loginCtn动态添加样式
    loginCtn.style.backgroundSize = "100% 100%"
    const yygfColor = "#f7943d"
    nextTick(() => {
      document
        .getElementsByTagName("body")[0]
        .style.setProperty("--primaryColor", "#f7943d")
      settingsStore.changeSetting({ key: "theme", value: yygfColor })
      handleThemeStyle(yygfColor)
    })
  }

  onMounted(() => {
    getCode()
    if (domainName.value === "yygf") {
      changeYYGFStyle()
    }
  })
</script>

<style lang="scss" scoped>
  $primaryColor: var(--primaryColor, #589cfd);
  .login-container {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: top;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;

    // .login_main {
    //   position: absolute;
    //   top: 250px;
    //   right: 25%;
    //   padding: 30px 0;
    // }
    .login-main {
      margin-top: -70px;
      width: 1240px;
      .login-main-title {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        .logo-img {
          max-height: 70px;
          max-width: 100%;
        }
        .login-main-title-name {
          font-size: 24px;
          border-left: 3px solid #5f7ffc;
          margin-left: 20px;
          padding-left: 20px;
          line-height: 30px;
          color: #1d4b88;
        }
      }
      .login-main-body {
        overflow: hidden;
        border-radius: 20px;
        box-shadow: 0px 0px 15px rgba(170, 170, 170, 0.6);
        background-color: white;
        display: flex;
        .login-main-body-left {
          width: 50%;
          > img {
            width: 100%;
            height: 100%;
          }
        }
        .login-main-body-right {
          width: 50%;
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          flex-direction: column;
          .login-main-body-right-title {
            font-size: 28px;
            color: #3a86f2;
          }
          .login-main-body-right-form {
            .login-form {
              width: 400px;
              .el-input {
                height: 40px;
                input {
                  height: 40px;
                }
              }
              .input-icon {
                height: 39px;
                width: 14px;
                margin-left: 0px;
              }
              .tel {
                display: flex;
                width: 100%;
                align-items: center;
                :deep(.el-input__wrapper) {
                  // background-color: #f2f2f2;
                  border-radius: 20px !important;

                  .front-inner {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    .front-type {
                      color: #7f7f7f;
                    }
                  }
                }
              }

              .code {
                display: flex;
                width: 100%;
                align-items: center;
                :deep(.el-input__wrapper) {
                  padding-left: 30px;
                  // background-color: #f2f2f2;
                  border-radius: 20px !important;

                  .end-inner {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 15px;
                    .end-type {
                      color: #4baef5;
                      cursor: pointer;
                    }

                    .end-interval {
                      color: #ccc;
                    }
                  }
                }
              }

              .tip {
                font-size: 12px;
                text-align: center;
                width: 100%;
                color: #aaaaaa;
                margin-top: -10px;
                margin-bottom: 20px;
              }
            }
            .login-tip {
              font-size: 13px;
              text-align: center;
              color: #bfbfbf;
            }
            .login-code {
              width: 33%;
              height: 40px;
              float: right;
              img {
                cursor: pointer;
                vertical-align: middle;
              }
            }
          }
        }
      }
    }
  }
  // .logo {
  //   display: flex;
  //   flex-direction: column;
  //   align-items: center;
  //   width: 100%;
  //   .title {
  //     max-height: 100%;
  //     display: flex;
  //     justify-content: center;
  //     align-items: center;
  //     color: $primaryColor;
  //     font-size: 26px;
  //     font-weight: 700;
  //     height: 20px;
  //     margin-bottom: 18px;
  //   }
  //   .logo-img {
  //     max-height: 70px;
  //     max-width: 100%;
  //     display: flex;
  //     justify-content: center;
  //     align-items: center;
  //     margin: 0px auto 30px auto;
  //     text-align: center;
  //     color: #707070;
  //   }
  // }

  // .login-form {
  //   border-radius: 6px;
  //   background: #ffffff;
  //   width: 400px;
  //   padding: 25px 25px 5px 25px;
  //   border: 1px solid #dcdfe6;
  //   box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.34901960784313724);
  //   .el-input {
  //     height: 40px;
  //     input {
  //       height: 40px;
  //     }
  //   }
  //   .input-icon {
  //     height: 39px;
  //     width: 14px;
  //     margin-left: 0px;
  //   }
  //   .tel {
  //     display: flex;
  //     width: 100%;
  //     align-items: center;
  //     :deep(.el-input__wrapper) {
  //       // background-color: #f2f2f2;
  //       border-radius: 20px !important;

  //       .front-inner {
  //         display: flex;
  //         align-items: center;
  //         justify-content: center;
  //         .front-type {
  //           color: #7f7f7f;
  //         }
  //       }
  //     }
  //   }

  //   .code {
  //     display: flex;
  //     width: 100%;
  //     align-items: center;
  //     :deep(.el-input__wrapper) {
  //       padding-left: 30px;
  //       // background-color: #f2f2f2;
  //       border-radius: 20px !important;

  //       .end-inner {
  //         display: flex;
  //         align-items: center;
  //         justify-content: center;
  //         margin-right: 15px;
  //         .end-type {
  //           color: #4baef5;
  //           cursor: pointer;
  //         }

  //         .end-interval {
  //           color: #ccc;
  //         }
  //       }
  //     }
  //   }

  //   .tip {
  //     font-size: 12px;
  //     text-align: center;
  //     width: 100%;
  //     color: #aaaaaa;
  //     margin-top: -10px;
  //     margin-bottom: 20px;
  //   }
  // }
  // .login-tip {
  //   font-size: 13px;
  //   text-align: center;
  //   color: #bfbfbf;
  // }
  // .login-code {
  //   width: 33%;
  //   height: 40px;
  //   float: right;
  //   img {
  //     cursor: pointer;
  //     vertical-align: middle;
  //   }
  // }
  .el-login-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: black;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }
  .login-code-img {
    height: 40px;
    padding-left: 12px;
  }

  :deep(.el-input--large .el-input__wrapper) {
    margin-left: 10px;
    padding: 1px 1px;
  }

  :deep(.el-input__prefix) {
    padding-left: 10px;
  }

  :deep(.el-input--large .el-input__inner) {
    padding-left: 10px;
  }

  :deep(.el-form-item__error) {
    margin-left: 10px;
  }
  :deep(.el-input__suffix-inner) {
    margin-right: 15px;
  }
</style>
